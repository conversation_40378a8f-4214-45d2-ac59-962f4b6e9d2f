using UnityEngine;
using UnityEngine.Timeline;

namespace UncleChenGames
{
    /// <summary>
    /// 关卡数据，用于存储单个关卡的各种配置信息
    /// </summary>
    [CreateAssetMenu(fileName = "Stage Data", menuName = "October/Stage Data")]
    public class StageData : ScriptableObject
    {
        [Header("Display Data")]
        [SerializeField] Sprite icon; // 关卡图标
        public Sprite Icon => icon; // 关卡图标

        [SerializeField] string displayName; // 关卡显示名称
        public string DisplayName => displayName; // 关卡显示名称

        [Header("Timeline Data")]
        [SerializeField] TimelineAsset timeline; // 关卡使用的时间轴资源
        public TimelineAsset Timeline => timeline; // 关卡使用的时间轴资源

        [Header("Stage Settings")]
        [SerializeField] StageType stageType; // 关卡类型
        public StageType StageType => stageType; // 关卡类型

        [SerializeField] StageFieldData stageFieldData; // 关卡场地数据
        public StageFieldData StageFieldData => stageFieldData; // 关卡场地数据

        [SerializeField] bool spawnProp; // 是否生成道具
        public bool SpawnProp => spawnProp; // 是否生成道具

        [SerializeField] bool removePropFromBossfight; // Boss战时是否移除道具
        public bool RemovePropFromBossfight => removePropFromBossfight; // Boss战时是否移除道具

        [Space]
        [SerializeField] Color spotlightColor; // 聚光灯颜色
        public Color SpotlightColor => spotlightColor; // 聚光灯颜色

        [SerializeField] Color spotlightShadowColor; // 聚光灯阴影颜色
        public Color SpotlightShadowColor => spotlightShadowColor; // 聚光灯阴影颜色

        [Space]
        [SerializeField] float enemyDamage; // 敌人伤害基础倍数
        public float EnemyDamage => enemyDamage;

        [SerializeField] float enemyHP;  // 敌人生命值基础倍数
        public float EnemyHP => enemyHP;

        [Space]
        [SerializeField] bool useCustomMusic; // 是否使用自定义音乐
        public bool UseCustomMusic => useCustomMusic; // 是否使用自定义音乐

        [SerializeField] string musicName; // 音乐名称
        public string MusicName => musicName; // 音乐名称

        [Header("Camera Settings")]
        [SerializeField] bool useCameraBounds = true; // 是否使用相机边界限制
        public bool UseCameraBounds => useCameraBounds; // 是否使用相机边界限制

        [SerializeField] Vector2 customBoundsSize; // 自定义边界大小（仅在需要时使用）
        public Vector2 CustomBoundsSize => customBoundsSize; // 自定义边界大小
    }

    /// <summary>
    /// 关卡类型枚举
    /// </summary>
    public enum StageType
    {
        Endless, // 无限模式
        VerticalEndless, // 垂直无限模式
        HorizontalEndless, // 水平无限模式
        Rect // 矩形边界模式
    }
}