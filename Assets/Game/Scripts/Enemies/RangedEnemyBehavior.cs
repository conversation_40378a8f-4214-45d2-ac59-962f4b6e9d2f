using UncleChenGames.Extensions;
using UncleChenGames.Pool;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace UncleChenGames.Enemy
{
    /// <summary>
    /// 远程敌人行为类，继承自敌人基类，添加了远程攻击能力
    /// </summary>
    public class RangedEnemyBehavior : EnemyBehavior
    {
        private static readonly int RANGED_ATTACK_TRIGGER = Animator.StringToHash("Ranged Attack"); // 远程攻击动画触发器哈希值

        [Header("Ranged Enemy Data")]
        [SerializeField] Animator animator; // 动画控制器
        [SerializeField] AttackType attackType = AttackType.AtPlayer; // 攻击类型

        [Space]
        [SerializeField, Min(1)] int minProjectilesPerAttack = 1; // 每次攻击最少弹丸数
        [SerializeField, Min(1)] int maxProjectilesPerAttack = 1; // 每次攻击最多弹丸数
        [SerializeField] float timeBetweenProjectiles = 0f; // 弹丸间的时间间隔
        [SerializeField] float projectileDamage = 1f; // 弹丸伤害
        [SerializeField, Range(0, 90f)] float projectileSpread = 0f; // 弹丸散布角度

        [Header("Overrides")]
        [SerializeField] bool overrideProjectileSpeed = false; // 是否覆盖弹丸速度
        [SerializeField, Min(0.01f)] float projectileSpeed = 1f; // 覆盖的弹丸速度
        [SerializeField] bool overrideProjectileLifetime = false; // 是否覆盖弹丸生命周期
        [SerializeField, Min(0.01f)] float projectileLifetime = 1f; // 覆盖的弹丸生命周期

        [Space]
        [SerializeField] GameObject projectilePrefab; // 弹丸预制体

        [Header("Shooting Behavior")]
        [SerializeField, Min(0)] float rangedAttackCooldown = 3; // 远程攻击冷却时间
        [SerializeField] bool useAttackAnimation = false; // 是否使用攻击动画
        [SerializeField, Min(0)] float minDistanceToPlayerForAttacking = 5f; // 攻击所需的最小距离
        private float minDistanceToPlayerForAttackingSqr; // 攻击所需最小距离的平方

        [Space]
        [Tooltip("Only works if 'useAttackAnimation' is true")]
        [SerializeField] bool changeSpeedDuringAttackAnimation = false; // 攻击动画期间是否改变速度
        [SerializeField, Min(0)] float speedDuringAttackAnimation = 1f; // 攻击动画期间的速度

        /// <summary>
        /// 对象池信息类，包含池实例和引用计数
        /// </summary>
        private class PoolInfo
        {
            public PoolComponent<SimpleEnemyProjectileBehavior> Pool { get; set; } // 对象池实例
            public int ReferenceCount { get; set; } // 引用计数
        }

        private static Dictionary<GameObject, PoolInfo> projectilePools = new Dictionary<GameObject, PoolInfo>(); // 弹丸对象池字典（带引用计数）

        private float lastTimeAttacked = 0; // 上次攻击时间

        protected override void Awake()
        {
            // 调用父类初始化
            base.Awake();
            // 如果该弹丸类型的对象池不存在，创建一个
            if (!projectilePools.ContainsKey(projectilePrefab))
            {
                var pool = new PoolComponent<SimpleEnemyProjectileBehavior>(projectilePrefab, maxProjectilesPerAttack * 2);
                projectilePools.Add(projectilePrefab, new PoolInfo { Pool = pool, ReferenceCount = 1 });
            }
            else
            {
                // 如果池已存在，增加引用计数
                projectilePools[projectilePrefab].ReferenceCount++;
            }
            // 预计算最小攻击距离的平方值
            minDistanceToPlayerForAttackingSqr = minDistanceToPlayerForAttacking * minDistanceToPlayerForAttacking;
        }

        protected override void Update()
        {
            // 调用父类更新逻辑
            base.Update();
            // 计算到玩家的距离平方
            var distanceToPlayer = (Center - PlayerBehavior.CenterPosition).sqrMagnitude;
            // 检查是否在攻击范围内且冷却时间已过
            if (distanceToPlayer < minDistanceToPlayerForAttackingSqr && lastTimeAttacked + rangedAttackCooldown < Time.time)
            {
                // 如果使用攻击动画
                if (useAttackAnimation)
                {
                    // 触发攻击动画
                    animator.SetTrigger(RANGED_ATTACK_TRIGGER);
                    // 如需要，改变攻击动画期间的速度
                    if (changeSpeedDuringAttackAnimation)
                    {
                        Speed = speedDuringAttackAnimation;
                    }
                }
                else
                {
                    // 直接攻击
                    Attack();
                }
                // 更新上次攻击时间
                lastTimeAttacked = Time.time;
            }
        }

        /// <summary>
        /// 执行远程攻击
        /// </summary>
        public virtual void Attack()
        {
            // 随机生成弹丸数量
            var projectilesCount = Random.Range(minProjectilesPerAttack, maxProjectilesPerAttack + 1);
            // 根据攻击类型执行不同的攻击模式
            if (attackType == AttackType.Circular)
            {
                StartCoroutine(CircularAttack(projectilesCount));
            }
            else
            {
                StartCoroutine(AtPlayerAttack(projectilesCount));
            }
        }

        /// <summary>
        /// 圆形攻击模式，在四周各个方向发射弹丸
        /// </summary>
        /// <param name="projectilesCount">弹丸数量</param>
        protected virtual IEnumerator CircularAttack(int projectilesCount)
        {
            // 弹丸间的等待时间
            var wait = new WaitForSeconds(timeBetweenProjectiles);
            // 获取指向玩家的方向（作为基准方向）
            var enemyToPlayerDirection = (PlayerBehavior.CenterPosition - Center).normalized;
            // 计算每个弹丸间的角度间隔
            var step = 360f / projectilesCount;
            // 发射所有弹丸
            for (int i = 0; i < projectilesCount; i++)
            {
                // 计算弹丸方向（包含随机散布）
                var projectileDirection = Quaternion.Euler(0, 0, i * step + Random.Range(-projectileSpread, projectileSpread)) * enemyToPlayerDirection;
                // 发射弹丸
                LaunchProjectile(projectileDirection);
                // 如果需要间隔，等待一段时间
                if (timeBetweenProjectiles > 0) yield return wait;
            }
        }

        /// <summary>
        /// 指向玩家的攻击模式，所有弹丸都指向玩家方向
        /// </summary>
        /// <param name="projectilesCount">弹丸数量</param>
        protected virtual IEnumerator AtPlayerAttack(int projectilesCount)
        {
            // 弹丸间的等待时间
            var wait = new WaitForSeconds(timeBetweenProjectiles);
            // 获取指向玩家的方向
            var enemyToPlayerDirection = (PlayerBehavior.CenterPosition - Center).normalized;
            // 发射所有弹丸
            for (int i = 0; i < projectilesCount; i++)
            {
                // 计算弹丸方向（包含随机散布）
                var projectileDirection = Quaternion.Euler(0, 0, Random.Range(-projectileSpread, projectileSpread)) * enemyToPlayerDirection;
                // 发射弹丸
                LaunchProjectile(projectileDirection);
                // 如果需要间隔，等待一段时间
                if (timeBetweenProjectiles > 0) yield return wait;
            }
        }

        /// <summary>
        /// 发射单个弹丸
        /// </summary>
        /// <param name="direction">弹丸飞行方向</param>
        protected virtual void LaunchProjectile(Vector2 direction)
        {
            // 从对象池获取弹丸实例
            var projectile = projectilePools[projectilePrefab].Pool.GetEntity();
            // 应用覆盖设置
            if (overrideProjectileLifetime) projectile.LifeTime = projectileLifetime;
            if (overrideProjectileSpeed) projectile.Speed = projectileSpeed;
            // 设置弹丸伤害
            projectile.Damage = projectileDamage;
            // 初始化弹丸
            projectile.Init(Center, direction);
        }

        /// <summary>
        /// 攻击动画结束时的回调函数（通过Animation Event调用）
        /// </summary>
        public void OnAttackAnimationEnded()
        {
            // 如果在攻击动画期间改变了速度，恢复原来的速度
            if (changeSpeedDuringAttackAnimation)
            {
                Speed = speed;
            }
        }

        /// <summary>
        /// 攻击类型枚举
        /// </summary>
        public enum AttackType
        {
            AtPlayer = 0, // 指向玩家的攻击
            Circular = 1, // 圆形攻击（全方位）
        }

        /// <summary>
        /// 组件销毁时的清理工作
        /// </summary>
        protected virtual void OnDestroy()
        {
            // 减少引用计数，只有当引用计数为0时才清理对象池
            if (projectilePools.ContainsKey(projectilePrefab))
            {
                projectilePools[projectilePrefab].ReferenceCount--;

                // 如果没有其他敌人使用这个池，才真正销毁它
                if (projectilePools[projectilePrefab].ReferenceCount <= 0)
                {
                    projectilePools[projectilePrefab].Pool.Destroy();
                    projectilePools.Remove(projectilePrefab);
                }
            }
        }
    }
}